const axios = require('axios');
const HttpStatus = require('http-status-codes');
const stringSimilarity = require('string-similarity');
const sampleOperationResp = require('./utils/operationResp');

const { allQueues } = require('./queues_v2/queues');
const db_resp = require('./utils/db_resp');
const users_model = require('./users_model');
const {
    getServiceWorkflowModel,
} = require('./queues_v2/processors/helpers/services_workflow_helper');
const {
    getParamsToServiceModel,
    setParamsToServicesModel,
} = require('./queues/processors/helpers');
const { get5MonthsDateRangeFilter } = require('./utils/helper');
const { isErrored } = require('form-data');

const getCurrentTime = () =>
    new Date().toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
    });

class va_ai_chatbot {
    async initVaAiChatBot(query) {
        try {
            console.log('va_ai_chatbot_model inside initVaAiChatBot', query);
            console.log(
                'va_ai_chatbot_model inside initVaAiChatBot :: ',
                JSON.stringify(this.userContext)
            );

            const parsedLatestMessage = JSON.parse(query?.latestMessage);
            const isNewChat = JSON.parse(query?.is_new_chat);
            const sessionId = query?.session_id;
            console.log(
                'va_ai_chatbot_model inside initVaAiChatBot :: parsedLatestMessage',
                parsedLatestMessage
            );
            const base_url = process.env.TMS_AGENT_URL;
            const isSrvcPrvdr = users_model.isServiceProvider(this.userContext);
            console.log(
                'va_ai_chatbot_model inside initVaAiChatBot :: isSrvcPrvdr',
                isSrvcPrvdr
            );
            const url = isSrvcPrvdr
                ? `${base_url}/tms/agent/chat/sp`
                : `${base_url}/tms/agent/chat/brand`;

            const response = await axios.post(url, {
                prompt: parsedLatestMessage?.text,
                user_context: this.userContext,
                timeline: parsedLatestMessage?.time,
                is_new_chat: isNewChat,
                session_id: sessionId,
            });
            // console.log('va_ai_chatbot :: response', response);
            console.log(
                'va_ai_chatbot_model :: response.data ::',
                response.data
            );
            console.log(
                'va_ai_chatbot_model :: response.data ::',
                response?.data?.response?.[0]
            );
            const jsonResp = response?.data?.response?.[0];

            // const parsed = JSON.parse(jsonStr);
            // console.log('va_ai_chatbot_model :: parsed ::', parsed);
            const finalResp = {
                from: 'tms-ai',
                text:
                    jsonResp?.content ||
                    `Hello Shambhu! I'm your AI assistant. How can I help you with your TMS operations today?`,
                time: getCurrentTime(),
                message: jsonResp,
                session_id: response?.data?.session_id,
            };
            console.log(
                'va_ai_chatbot_model :: finalResp ::',
                JSON.stringify(finalResp)
            );
            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: initVaAiChatBot :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                'failed',
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async getPossibleBrandLists(query) {
        try {
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandLists',
                query
            );
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandLists :: ',
                JSON.stringify(query.user_context)
            );
            const providedBrandName = query?.brand_name;
            let modifiedQuery = {};
            modifiedQuery['org_id'] = users_model.getOrgId(query.user_context);
            modifiedQuery['usr_id'] = users_model.getUUID(query.user_context);
            modifiedQuery['provided_brand_name'] = providedBrandName;
            // console.log('modifiedQuery', modifiedQuery);
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: providedBrandName',
                providedBrandName
            );
            const form_data = JSON.stringify(modifiedQuery);
            const response =
                await this.db.tms_get_possible_customer_orgs_fr_user(
                    modifiedQuery.org_id,
                    modifiedQuery.usr_id
                );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: response',
                response
            );
            const dbResp = new db_resp(
                response[0].tms_get_possible_customer_orgs_fr_user
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: dbResp',
                JSON.stringify(dbResp)
            );

            if (!dbResp?.status) {
                if (dbResp?.code == 'is_unauthorized') {
                    return new sampleOperationResp(
                        false,
                        {
                            data: dbResp?.data,
                            status: 'UNAUTHORIZED_BRAND',
                        },
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    );
                }
                return new sampleOperationResp(
                    false,
                    {
                        data: [],
                        status: 'INTERNAL_SERVER_ERROR',
                    },
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            const possibleBrandList = dbResp?.data;
            const filteredArray = possibleBrandList.filter(
                (singleBrand) =>
                    singleBrand.toLowerCase() ===
                    providedBrandName.toLowerCase()
            );

            let finalResp = {};
            if (!providedBrandName || providedBrandName == '') {
                finalResp = {
                    data: possibleBrandList,
                    status: 'MISSING_SHOW_ALL',
                };
            } else if (filteredArray?.length > 0) {
                finalResp = {
                    data: filteredArray,
                    status: 'BRAND_VERIFIED',
                };
            } else {
                // Use string similarity to find the best match
                const matches = stringSimilarity.findBestMatch(
                    providedBrandName,
                    possibleBrandList
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandLists :: matches',
                    matches
                );
                const ratings = matches.ratings;
                const bestMatch = matches.bestMatch;
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandLists :: ratings',
                    ratings
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandLists :: bestMatch',
                    matches
                );
                // Step 1: Sort by rating DESC
                const sortedRatings = ratings.sort(
                    (a, b) => b.rating - a.rating
                );

                // Step 2: Reduce and collect only targets above threshold
                const bestMatches = sortedRatings.reduce(
                    (acc, { target, rating }) => {
                        if (rating > 0.4) acc.push(target);
                        return acc;
                    },
                    []
                );
                if (bestMatches?.length > 0) {
                    // threshold can be adjusted
                    finalResp = {
                        data: bestMatches,
                        status: 'PARTIAL_MATCH_FOUND',
                    };
                } else {
                    finalResp = {
                        data: possibleBrandList,
                        status: 'NO_MATCH',
                    };
                }
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: response',
                response
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: dbResp ::',
                JSON.stringify(dbResp?.data)
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: finalResp ::',
                JSON.stringify(finalResp)
            );

            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandLists :: sendGaiRatingsMail :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                {
                    data: [],
                    status: 'INTERNAL_SERVER_ERROR',
                },
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async getPossibleBrandSrvcTypeLists(query) {
        try {
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandSrvcTypeLists',
                query
            );
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandSrvcTypeLists :: ',
                JSON.stringify(query.user_context)
            );
            const providedBrandName = query?.brand_name;
            const srvcTypeName = query?.srvc_type_name;
            let modifiedQuery = {};
            modifiedQuery['org_id'] = users_model.getOrgId(query.user_context);
            modifiedQuery['usr_id'] = users_model.getUUID(query.user_context);
            modifiedQuery['provided_brand_name'] = providedBrandName;
            modifiedQuery['provided_srvc_type_name'] = srvcTypeName;
            // console.log('modifiedQuery', modifiedQuery);
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: srvcTypeName',
                srvcTypeName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: providedBrandName',
                providedBrandName
            );
            const form_data = JSON.stringify(modifiedQuery);
            const response = await this.db.tms_get_possible_srvc_types_org(
                modifiedQuery.org_id,
                modifiedQuery.usr_id,
                providedBrandName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: response',
                response
            );
            const dbResp = new db_resp(
                response[0].tms_get_possible_srvc_types_org
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: dbResp',
                JSON.stringify(dbResp)
            );

            if (!dbResp?.status) {
                return new sampleOperationResp(
                    false,
                    {
                        data: [],
                        status: 'INTERNAL_SERVER_ERROR',
                    },
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            const possibleSrvcTypesOfBrand = dbResp?.data;
            const filteredArray = possibleSrvcTypesOfBrand.filter(
                (singleBrand) =>
                    singleBrand.toLowerCase() === srvcTypeName.toLowerCase()
            );

            let finalResp = {};
            if (!srvcTypeName || srvcTypeName == '') {
                finalResp = {
                    data: possibleSrvcTypesOfBrand,
                    status: 'MISSING_SHOW_ALL',
                };
            } else if (filteredArray?.length > 0) {
                finalResp = {
                    data: filteredArray,
                    status: 'SRVC_TYPE_VERIFIED',
                };
            } else {
                // Use string similarity to find the best match
                const matches = stringSimilarity.findBestMatch(
                    srvcTypeName,
                    possibleSrvcTypesOfBrand
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: matches',
                    matches
                );
                const ratings = matches.ratings;
                const bestMatch = matches.bestMatch;
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: ratings',
                    ratings
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: bestMatch',
                    matches
                );
                // Step 1: Sort by rating DESC
                const sortedRatings = ratings.sort(
                    (a, b) => b.rating - a.rating
                );

                // Step 2: Reduce and collect only targets above threshold
                const bestMatches = sortedRatings.reduce(
                    (acc, { target, rating }) => {
                        if (rating > 0.4) acc.push(target);
                        return acc;
                    },
                    []
                );
                if (bestMatches?.length > 0) {
                    // threshold can be adjusted
                    finalResp = {
                        data: bestMatches,
                        status: 'PARTIAL_MATCH_FOUND',
                    };
                } else {
                    finalResp = {
                        data: possibleSrvcTypesOfBrand,
                        status: 'NO_MATCH',
                    };
                }
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: response',
                response
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: dbResp ::',
                JSON.stringify(dbResp?.data)
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: finalResp ::',
                JSON.stringify(finalResp)
            );

            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeLists :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                {
                    data: [],
                    status: 'INTERNAL_SERVER_ERROR',
                },
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async getPossibleBrandSrvcTypeStatuses(query) {
        try {
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandSrvcTypeStatuses',
                query
            );
            console.log(
                'va_ai_chatbot_model inside getPossibleBrandSrvcTypeStatuses :: ',
                JSON.stringify(query.user_context)
            );
            const providedBrandName = query?.brand_name;
            const srvcTypeName = query?.srvc_type_name;
            const providedSrvcStatus = query?.status_name;
            let modifiedQuery = {};
            modifiedQuery['org_id'] = users_model.getOrgId(query.user_context);
            modifiedQuery['usr_id'] = users_model.getUUID(query.user_context);
            modifiedQuery['provided_brand_name'] = providedBrandName;
            modifiedQuery['provided_srvc_type_name'] = srvcTypeName;
            // console.log('modifiedQuery', modifiedQuery);
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: srvcTypeName',
                srvcTypeName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: providedBrandName',
                providedBrandName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: providedSrvcStatus',
                providedSrvcStatus
            );
            const form_data = JSON.stringify(modifiedQuery);
            const response = await this.db.tms_get_srvc_type_statuses(
                modifiedQuery.org_id,
                modifiedQuery.usr_id,
                providedBrandName,
                srvcTypeName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: response',
                response
            );
            const dbResp = new db_resp(response[0].tms_get_srvc_type_statuses);
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: dbResp',
                JSON.stringify(dbResp)
            );

            if (!dbResp?.status) {
                return new sampleOperationResp(
                    false,
                    {
                        data: [],
                        status: 'INTERNAL_SERVER_ERROR',
                    },
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            const possibleSrvcTypesStatuses = dbResp?.data;
            const filteredStatusArray = possibleSrvcTypesStatuses.filter(
                (singleStatus) =>
                    singleStatus.toLowerCase() ===
                    providedSrvcStatus.toLowerCase()
            );

            let finalResp = {};
            if (!providedSrvcStatus || providedSrvcStatus == '') {
                finalResp = {
                    data: possibleSrvcTypesStatuses,
                    status: 'MISSING_SHOW_ALL',
                };
            } else if (filteredStatusArray?.length > 0) {
                finalResp = {
                    data: filteredStatusArray,
                    status: 'STATUS_VERIFIED',
                };
            } else {
                //No match Condition
                // Use string similarity to find the best match
                const statusMatches = stringSimilarity.findBestMatch(
                    providedSrvcStatus,
                    possibleSrvcTypesStatuses
                );

                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: statusMatches',
                    statusMatches
                );
                const statusRatings = statusMatches.ratings;
                console.log(
                    'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: statusRatings',
                    statusRatings
                );
                // Step 1: Sort by rating DESC
                const sortedRatingsFrStatuses = statusRatings.sort(
                    (a, b) => b.rating - a.rating
                );

                // Step 2: Reduce and collect only targets above threshold
                const bestMatchesFrStatus = sortedRatingsFrStatuses.reduce(
                    (acc, { target, rating }) => {
                        if (rating > 0.4) acc.push(target);
                        return acc;
                    },
                    []
                );

                if (bestMatchesFrStatus?.length > 0) {
                    finalResp = {
                        data: bestMatchesFrStatus,
                        status: 'PARTIAL_MATCH_FOUND',
                    };
                } else {
                    finalResp = {
                        data: possibleSrvcTypesStatuses,
                        status: 'NO_MATCH',
                    };
                }
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: response',
                response
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: dbResp ::',
                JSON.stringify(dbResp?.data)
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: finalResp ::',
                JSON.stringify(finalResp)
            );

            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: getPossibleBrandSrvcTypeStatuses :: sendGaiRatingsMail :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                {
                    data: [],
                    status: 'INTERNAL_SERVER_ERROR',
                },
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async getPossibleSrvcRequests(query) {
        try {
            console.log(
                'va_ai_chatbot_model inside getPossibleSrvcRequests',
                query
            );
            console.log(
                'va_ai_chatbot_model inside getPossibleSrvcRequests :: ',
                JSON.stringify(query.user_context)
            );
            const providedBrandName = query?.brand_name;
            const srvcTypeName = query?.srvc_type_name;
            const providedCurrentSrvcStatus = query?.current_status_name;
            let modifiedQuery = {};
            modifiedQuery['org_id'] = users_model.getOrgId(query.user_context);
            modifiedQuery['usr_id'] = users_model.getUUID(query.user_context);
            modifiedQuery['is_frontend'] = true;
            modifiedQuery['provided_brand_name'] = providedBrandName;
            modifiedQuery['provided_srvc_type_name'] = srvcTypeName;
            modifiedQuery['provided_current_status'] =
                providedCurrentSrvcStatus;
            // // 👉 Add filters with dynamic date range (1 year back to now)
            // const now = new Date();
            // const oneYearAgo = new Date();
            // oneYearAgo.setFullYear(now.getFullYear() - 1);

            modifiedQuery['filters'] = {
                creation_srvc_req_date: get5MonthsDateRangeFilter(),
            };

            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: srvcTypeName',
                srvcTypeName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: providedBrandName',
                providedBrandName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: providedCurrentSrvcStatus',
                providedCurrentSrvcStatus
            );
            const form_data = JSON.stringify(modifiedQuery);
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: form_data',
                form_data
            );

            const response = await this.db.tms_get_srvc_reqs_v3(form_data);
            console.log('response', response);
            const dbResp = new db_resp(response[0].tms_get_srvc_reqs_v3);
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: dbResp',
                JSON.stringify(dbResp)
            );

            if (!dbResp?.status) {
                return new sampleOperationResp(
                    false,
                    {
                        data: [],
                        status: 'INTERNAL_SERVER_ERROR',
                    },
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }
            const possibleSrvcReqs = dbResp?.data;
            const srvcReqsArray =
                possibleSrvcReqs.length >= 50
                    ? possibleSrvcReqs.slice(0, 50)
                    : possibleSrvcReqs;
            let finalResp = {};

            if (srvcReqsArray?.length > 0) {
                finalResp = {
                    data: srvcReqsArray,
                    status: 'SRVC_REQS_FOUND',
                    total_count: possibleSrvcReqs?.length,
                };
            } else {
                finalResp = {
                    data: possibleSrvcReqs,
                    status: 'SRVC_REQS_NOT_FOUND',
                };
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: response',
                response
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: dbResp ::',
                JSON.stringify(dbResp?.data)
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: finalResp ::',
                JSON.stringify(finalResp)
            );

            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequests :: sendGaiRatingsMail :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                {
                    data: [],
                    status: 'INTERNAL_SERVER_ERROR',
                },
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async getPossibleSrvcRequestsAndUpdate(query) {
        try {
            console.log(
                'va_ai_chatbot_model inside getPossibleSrvcRequestsAndUpdate',
                query
            );
            console.log(
                'va_ai_chatbot_model inside getPossibleSrvcRequestsAndUpdate :: ',
                JSON.stringify(query.user_context)
            );
            const calledThrWrkflw = query?.call_thr_wrkflw;
            const providedBrandName = query?.brand_name;
            const srvcTypeName = query?.srvc_type_name;
            const providedCurrentSrvcStatus = query?.current_status_name;
            const providedNewSrvcStatus = query?.new_status_name;
            const is_customer_access = users_model.isServiceProvider(
                query.user_context
            )
                ? 1
                : 0;
            let modifiedQuery = calledThrWrkflw ? query : {};
            let batch_data = calledThrWrkflw ? query?.batch_data : [];
            let srvcTypeId;
            if (!calledThrWrkflw) {
                modifiedQuery['org_id'] = users_model.getOrgId(
                    query.user_context
                );
                modifiedQuery['usr_id'] = users_model.getUUID(
                    query.user_context
                );
                modifiedQuery['is_bulk_update'] = 1;
                modifiedQuery['is_frontend'] = true;
                modifiedQuery['provided_brand_name'] = providedBrandName;
                modifiedQuery['provided_srvc_type_name'] = srvcTypeName;
                modifiedQuery['provided_current_status'] =
                    providedCurrentSrvcStatus;
                modifiedQuery['provided_new_status'] = providedNewSrvcStatus;

                // 👉 Add filters with dynamic date range (1 year back to now)
                // const now = new Date();
                // const oneYearAgo = new Date();
                // oneYearAgo.setFullYear(now.getFullYear() - 1);

                modifiedQuery['filters'] = {
                    creation_srvc_req_date: get5MonthsDateRangeFilter(),
                };

                console.log(
                    'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: modifiedQuery.filters',
                    modifiedQuery?.filters
                );
                const form_data_to_fetch_srvc_res =
                    JSON.stringify(modifiedQuery);
                const srvcReqsDataToUpdate =
                    await this.db.tms_get_srvc_reqs_to_update_fr_va(
                        form_data_to_fetch_srvc_res
                    );
                console.log('srvcReqsDataToUpdate', srvcReqsDataToUpdate);
                const srvcReqsDataToUpdateDbResp = new db_resp(
                    srvcReqsDataToUpdate[0].tms_get_srvc_reqs_to_update_fr_va
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: srvcReqsDataToUpdateDbResp',
                    srvcReqsDataToUpdateDbResp
                );
                batch_data = srvcReqsDataToUpdateDbResp?.data?.batch_data || [];
                srvcTypeId = srvcReqsDataToUpdateDbResp?.data?.srvc_type_id;
                modifiedQuery['dynamic_form_data'] =
                    srvcReqsDataToUpdateDbResp?.data?.srvc_type_config_data;
                modifiedQuery['batch_data'] = batch_data;
                modifiedQuery['is_agent_update'] = true;
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: srvcTypeName',
                srvcTypeName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: providedBrandName',
                providedBrandName
            );
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: providedCurrentSrvcStatus',
                providedCurrentSrvcStatus
            );
            delete modifiedQuery['filters'];
            let UpdationResp;
            if (batch_data?.length > 40 && !calledThrWrkflw) {
                const services_model = getParamsToServiceModel(
                    {
                        ...this,
                        srvc_type_id: srvcTypeId,
                        user_context: query.user_context,
                    },
                    this.db
                );

                let workflow = getServiceWorkflowModel(services_model);
                workflow.triggerSrvcReqUpdationWorkFlowFrmVA({
                    ...modifiedQuery,
                    user_context: query.user_context,
                });
                return new sampleOperationResp(
                    true,
                    {
                        data: [],
                        total_count: batch_data?.length,
                        status: 'PROCESSED_IN_QUEUE',
                    },
                    HttpStatus.StatusCodes.OK
                );
            } else {
                const form_data = JSON.stringify(modifiedQuery);
                console.log(
                    'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: form_data',
                    form_data
                );

                const response =
                    await this.db.tms_create_srvc_reqs_batch_v2(form_data);

                const dbResp = new db_resp(
                    response[0].tms_create_srvc_reqs_batch_v2
                );

                if (!dbResp?.status && dbResp?.code == 'Internal_error') {
                    return new sampleOperationResp(
                        false,
                        {
                            data: [],
                            status: 'INTERNAL_SERVER_ERROR',
                        },
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    );
                }

                const services_model = getParamsToServiceModel(
                    {
                        ...this,
                        srvc_type_id: dbResp?.data?.srvc_type_id,
                        user_context: query.user_context,
                    },
                    this.db
                );

                let workflow = getServiceWorkflowModel(services_model);

                const entryIdsVsQuery = dbResp?.data?.entry_ids_vs_query;

                for (const service_req_id in entryIdsVsQuery) {
                    if (entryIdsVsQuery.hasOwnProperty(service_req_id)) {
                        const singleEntry = entryIdsVsQuery[service_req_id];

                        workflow.trigger(
                            singleEntry,
                            service_req_id,
                            new db_resp({
                                status: true,
                                code: 'success',
                                data: {
                                    entry_id: service_req_id,
                                    display_code: singleEntry?.tms_display_code,
                                },
                            }),
                            is_customer_access
                        );
                    }
                }

                UpdationResp = dbResp?.data;

                console.log(
                    'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: response',
                    response
                );
                console.log(
                    'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: dbResp ::',
                    JSON.stringify(dbResp?.data)
                );
            }
            const srvcReqUpdationResp = UpdationResp || {};
            const successEntries = srvcReqUpdationResp?.success_entries || [];
            const failedEntries = srvcReqUpdationResp?.failed_entries || [];

            let finalResp;

            if (successEntries?.length > 0 && failedEntries?.length > 0) {
                finalResp = {
                    data: failedEntries,
                    status: 'PARTIALLY_UPDATED',
                };
            } else if (
                successEntries?.length > 0 &&
                failedEntries?.length == 0
            ) {
                finalResp = {
                    data: successEntries,
                    status: 'ALL_UPDATED',
                };
            } else if (
                successEntries?.length == 0 &&
                failedEntries?.length > 0
            ) {
                finalResp = {
                    data: failedEntries,
                    status: 'ALL_FAILED',
                };
            }

            if (calledThrWrkflw) {
                finalResp['success_entries'] = successEntries;
                finalResp['failed_entries'] = failedEntries;
            }

            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: finalResp ::',
                JSON.stringify(finalResp)
            );

            return new sampleOperationResp(
                true,
                finalResp,
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: getPossibleSrvcRequestsAndUpdate :: error :: ',
                error?.message
            );

            return new sampleOperationResp(
                true,
                {
                    data: [],
                    status: 'INTERNAL_SERVER_ERROR',
                },
                HttpStatus.StatusCodes.BAD_REQUEST
            );
        }
    }

    async createVARating(query) {
        try {
            // Extract required parameters from query
            const sessionId = query?.session_id;
            const additionalComments = query?.additional_comments;
            const suggFeedback = query?.sugg_feedback;
            const usrPrompt = query?.usr_prompt;
            const vaResponse = query?.va_response;
            const goodRating = query?.good_rating;
            const badRating = query?.bad_rating;

            // Validate required fields
            if (!sessionId) {
                return new sampleOperationResp(
                    false,
                    'Session ID is required',
                    HttpStatus.StatusCodes.BAD_REQUEST
                );
            }

            // Prepare form data for database function
            const formData = {
                org_id: users_model.getOrgId(this.userContext),
                usr_id: users_model.getUUID(this.userContext),
                ip_address: this.ip_address,
                user_agent: this.user_agent_,
                session_id: sessionId,
                additional_comments: additionalComments || null,
                sugg_feedback: suggFeedback || null,
                usr_prompt: usrPrompt || null,
                va_response: vaResponse || null,
                good_rating: goodRating || false,
                bad_rating: badRating || false,
            };

            console.log(
                'va_ai_chatbot_model :: createVARating :: formData',
                JSON.stringify(formData)
            );

            // Call database function
            const response = await this.db.tms_create_va_rating(
                JSON.stringify(formData)
            );

            console.log(
                'va_ai_chatbot_model :: createVARating :: response',
                response
            );

            const dbResp = new db_resp(response[0].tms_create_va_rating);

            if (!dbResp?.status) {
                if (dbResp?.code === 'entry_already_exists') {
                    return new sampleOperationResp(
                        true,
                        {
                            message: 'Rating already exists for this session',
                            data: dbResp?.data,
                        },
                        HttpStatus.StatusCodes.OK
                    );
                }

                return new sampleOperationResp(
                    false,
                    {
                        message: 'Failed to create VA rating',
                        error: dbResp?.code || 'Internal error',
                    },
                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                );
            }

            return new sampleOperationResp(
                true,
                {
                    message: 'VA rating created successfully',
                    data: dbResp?.data,
                },
                HttpStatus.StatusCodes.OK
            );
        } catch (error) {
            console.log(
                'va_ai_chatbot_model :: createVARating :: error ::',
                error
            );

            return new sampleOperationResp(
                false,
                {
                    message: 'Internal server error',
                    error: error?.message,
                },
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            );
        }
    }

    getVaAiChatBotModelData(services_model) {
        return {
            ip_address: services_model.ip_address,
            user_agent: services_model.user_agent_,
            user_context: services_model.user_context,
        };
    }

    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }

    getFreshInstance(model) {
        const clonedInstance = new va_ai_chatbot();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new va_ai_chatbot();
